"""
AI Dashboard Layouts Module for EOTS v2.5
=========================================

This module contains all layout and panel assembly functions for the AI dashboard including:
- Panel creation and assembly
- Grid layouts and responsive design
- Component organization
- Container management
- Layout utilities

Author: EOTS v2.5 Development Team
Version: 2.5.0
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from dash import dcc, html
import plotly.graph_objects as go

from data_models.eots_schemas_v2_5 import (
    FinalAnalysisBundleV2_5,
    ProcessedDataBundleV2_5,
    EOTSConfigV2_5
)

from .components import (
    AI_COLORS, AI_TYPOGRAPHY, AI_SPACING, AI_EFFECTS,
    create_placeholder_card, create_enhanced_confidence_meter,
    create_quick_action_buttons, create_regime_transition_indicator,
    get_unified_card_style, get_unified_text_style, get_card_style,
    create_clickable_title_with_info
)

from .visualizations import (
    create_market_state_visualization, create_enhanced_market_state_visualization,
    create_confidence_meter, create_confluence_gauge, create_ai_performance_chart,
    create_regime_transition_gauge
)

from .intelligence import (
    generate_unified_ai_insights_sync, generate_ai_market_insights,
    calculate_ai_confidence_sync, calculate_enhanced_ai_confidence,
    calculate_recommendation_confidence, generate_enhanced_ai_recommendations,
    analyze_enhanced_regime_with_ai, calculate_enhanced_regime_confidence,
    calculate_regime_transition_probability, get_consolidated_intelligence_data,
    calculate_system_health_score
)

# Import centralized regime display utilities
from dashboard_application.utils.regime_display_utils import get_tactical_regime_name, get_regime_color_class, get_regime_icon

logger = logging.getLogger(__name__)

# ===== AI DASHBOARD MODULE INFORMATION BLURBS =====

AI_MODULE_INFO = {
    "unified_intelligence": """🧠 Unified AI Intelligence Hub: This is your COMMAND CENTER for all AI-powered market analysis. The 4-quadrant layout provides: TOP-LEFT: AI Confidence Barometer showing system conviction levels with real-time data quality scoring. TOP-RIGHT: Signal Confluence Barometer measuring agreement between multiple EOTS metrics (VAPI-FA, DWFD, TW-LAF, GIB). BOTTOM-LEFT: Unified Intelligence Analysis combining Alpha Vantage news sentiment, MCP server insights, and ATIF recommendations. BOTTOM-RIGHT: Market Dynamics Radar showing 6-dimensional market forces (Volatility, Flow, Momentum, Structure, Sentiment, Risk). 💡 TRADING INSIGHT: When AI Confidence > 80% AND Signal Confluence > 70% = HIGH CONVICTION setup. Watch for Market Dynamics radar showing EXTREME readings (outer edges) = potential breakout/breakdown. The Unified Intelligence text provides CONTEXTUAL NARRATIVE explaining WHY the system is confident. This updates every 15 minutes with fresh data integration!""",

    "regime_analysis": """🌊 AI Regime Analysis: This 4-quadrant system identifies and analyzes the CURRENT MARKET REGIME using advanced EOTS metrics. TOP-LEFT: Regime Confidence Barometer showing conviction in current regime classification with transition risk assessment. TOP-RIGHT: Regime Characteristics Analysis displaying 4 key market properties (Volatility, Flow Direction, Risk Level, Momentum) with DYNAMIC COLOR CODING. BOTTOM-LEFT: Enhanced AI Analysis showing current regime name, key Z-score metrics (VAPI-FA, DWFD, TW-LAF), and AI-generated insights. BOTTOM-RIGHT: Transition Gauge measuring probability of regime change with stability metrics. 💡 TRADING INSIGHT: Regime Confidence > 70% = STABLE regime, trade WITH the characteristics. Transition Risk > 60% = UNSTABLE regime, expect volatility and potential reversals. When characteristics show EXTREME values (Very High/Low) = regime at INFLECTION POINT. Use regime insights to adjust position sizing and strategy selection!""",

    "recommendations": """🎯 AI Recommendations Engine: This panel displays ADAPTIVE TRADE IDEA FRAMEWORK (ATIF) generated strategies with AI-enhanced conviction scoring. Each recommendation includes: Strategy Type, Conviction Level (0-100%), AI-generated Rationale, and Risk Assessment. The system combines EOTS metrics, regime analysis, and market structure to generate ACTIONABLE trade ideas. 💡 TRADING INSIGHT: Conviction > 80% = HIGH PROBABILITY setup, consider larger position size. Conviction 60-80% = MODERATE setup, standard position size. Conviction < 60% = LOW PROBABILITY, small position or avoid. When multiple recommendations AGREE on direction = STRONG CONFLUENCE. Pay attention to the AI rationale - it explains the LOGIC behind each recommendation. Recommendations update based on changing market conditions and new data!"""
}

# ===== MAIN PANEL CREATION FUNCTIONS =====

def create_unified_ai_intelligence_hub(bundle_data: FinalAnalysisBundleV2_5, ai_settings: Dict[str, Any], symbol: str, db_manager=None) -> html.Div:
    """Create the UNIFIED AI Intelligence Hub with 4-quadrant layout - enhanced Market Dynamics Radar as the star."""
    try:
        # Extract comprehensive data for unified analysis using Pydantic models
        processed_data = bundle_data.processed_data_bundle
        metrics = processed_data.underlying_data_enriched.model_dump() if processed_data else {}
        regime = getattr(processed_data.underlying_data_enriched, 'current_market_regime_v2_5', 'UNKNOWN') if processed_data else 'UNKNOWN'

        # Calculate unified intelligence metrics
        confidence_score = calculate_ai_confidence_sync(bundle_data, db_manager)
        confluence_score = calculate_metric_confluence_score(metrics)
        signal_strength = assess_signal_strength(metrics)

        # Generate unified AI insights
        unified_insights = generate_unified_ai_insights_sync(bundle_data, symbol)

        # Create enhanced market dynamics radar (STAR OF THE SHOW)
        enhanced_market_radar = create_enhanced_market_dynamics_radar(bundle_data, symbol)

        # UNIFIED 4-QUADRANT LAYOUT
        return html.Div([
            # Outer colored container
            html.Div([
                # Inner dark card container
                html.Div([
                    # Card Header with clickable title and info
                    html.Div([
                        create_clickable_title_with_info(
                            "🧠 Unified AI Intelligence Hub",
                            "unified_intelligence",
                            AI_MODULE_INFO["unified_intelligence"]
                        )
                    ], className="card-header", style={
                        "background": "transparent",
                        "borderBottom": f"2px solid {AI_COLORS['primary']}",
                        "padding": f"{AI_SPACING['md']} {AI_SPACING['xl']}"
                    }),

                    # Card Body - 4 QUADRANT LAYOUT
                    html.Div([
                        # TOP ROW - Quadrants 1 & 2
                        html.Div([
                            # QUADRANT 1: AI Confidence Barometer (Top Left)
                            html.Div([
                                create_ai_confidence_barometer(confidence_score, bundle_data, db_manager)
                            ], className="col-md-6 mb-3"),

                            # QUADRANT 2: Signal Confluence Barometer (Top Right)
                            html.Div([
                                create_signal_confluence_barometer(confluence_score, metrics, signal_strength)
                            ], className="col-md-6 mb-3")
                        ], className="row"),

                        # BOTTOM ROW - Quadrants 3 & 4
                        html.Div([
                            # QUADRANT 3: Unified Intelligence Analysis (Bottom Left)
                            html.Div([
                                create_unified_intelligence_analysis(unified_insights, regime, bundle_data)
                            ], className="col-md-6"),

                            # QUADRANT 4: Enhanced Market Dynamics Radar (Bottom Right) - STAR OF THE SHOW
                            html.Div([
                                create_market_dynamics_radar_quadrant(enhanced_market_radar, metrics, symbol)
                            ], className="col-md-6")
                        ], className="row")
                    ], className="card-body", style={
                        "padding": f"{AI_SPACING['lg']} {AI_SPACING['xl']}",
                        "background": "transparent"
                    })
                ], className="card h-100")
            ], style=get_card_style('primary'))
        ], className="ai-intelligence-hub")

    except Exception as e:
        logger.error(f"Error creating unified AI intelligence hub: {str(e)}")
        return create_placeholder_card("🧠 Unified AI Intelligence Hub", f"Error: {str(e)}")


def create_ai_recommendations_panel(bundle_data: FinalAnalysisBundleV2_5, ai_settings: Dict[str, Any], symbol: str) -> html.Div:
    """Create enhanced AI-powered recommendations panel with comprehensive EOTS integration."""
    try:
        atif_recs = bundle_data.atif_recommendations_v2_5 or []

        # Calculate recommendation confidence and priority
        rec_confidence = calculate_recommendation_confidence(bundle_data, atif_recs)

        # Extract EOTS metrics for enhanced recommendations using Pydantic models
        processed_data = bundle_data.processed_data_bundle
        metrics = processed_data.underlying_data_enriched.model_dump() if processed_data else {}

        # Generate AI-enhanced insights
        ai_insights = generate_enhanced_ai_recommendations(bundle_data, symbol)

        # UNIFIED NESTED CONTAINER STRUCTURE
        return html.Div([
            # Outer colored container
            html.Div([
                # Inner dark card container
                html.Div([
                    # Card Header with clickable title and info
                    html.Div([
                        create_clickable_title_with_info(
                            "🎯 AI Recommendations",
                            "recommendations",
                            AI_MODULE_INFO["recommendations"]
                        )
                    ], className="card-header", style={
                        "background": "transparent",
                        "borderBottom": f"2px solid {AI_COLORS['secondary']}",
                        "padding": f"{AI_SPACING['md']} {AI_SPACING['xl']}"
                    }),

                    # Card Body
                    html.Div([
                        # Confidence badge
                        html.Div([
                            html.Span(f"Confidence: {rec_confidence:.0%}",
                                     id="recommendations-confidence",
                                     className="badge mb-3",
                                     style={
                                         "background": AI_COLORS['success'] if rec_confidence > 0.7 else AI_COLORS['warning'],
                                         "color": "white",
                                         "fontSize": AI_TYPOGRAPHY['small_size']
                                     })
                        ]),

                # Quick Actions
                create_quick_action_buttons(bundle_data, symbol),

                # Market Context
                html.Div([
                    html.H6("📊 Market Context", className="mb-2", style={
                        "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                        "color": AI_COLORS['dark']
                    }),
                    html.Div([
                        html.P(f"Symbol: {symbol}", className="small mb-1", style={"color": AI_COLORS['muted']}),
                        html.P(f"Market Regime: {getattr(bundle_data.processed_data_bundle.underlying_data_enriched, 'current_market_regime_v2_5', 'Unknown')}",
                              className="small mb-1", style={"color": AI_COLORS['muted']}),
                        html.P(f"Analysis Time: {bundle_data.bundle_timestamp.strftime('%H:%M:%S')}",
                              className="small mb-0", style={"color": AI_COLORS['muted']})
                    ])
                ], className="mb-3"),

                # ATIF Recommendations Section
                html.Div([
                    html.Div([
                        html.H6("🎯 ATIF Strategy Recommendations", className="mb-0", style={
                            "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                            "color": AI_COLORS['dark']
                        }),
                        html.Small(f"Count: {len(atif_recs)}", id="recommendations-count", style={
                            "color": AI_COLORS['muted'],
                            "fontSize": AI_TYPOGRAPHY['tiny_size']
                        })
                    ], className="d-flex justify-content-between align-items-center mb-3"),
                    html.Div([
                        create_atif_recommendation_items(atif_recs[:3]) if atif_recs else
                        html.P("No ATIF recommendations available", className="text-muted", style={"color": AI_COLORS['muted']})
                    ])
                ], className="mb-3"),

                # AI Enhanced Recommendations
                html.Div([
                    html.H6("🧠 AI Enhanced Insights", className="mb-3", style={
                        "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                        "color": AI_COLORS['dark']
                    }),
                    html.Div([
                        html.Div([
                            html.P(insight, className="insight-item mb-2", style={
                                "fontSize": AI_TYPOGRAPHY['small_size'],
                                "padding": f"{AI_SPACING['sm']} {AI_SPACING['md']}",
                                "background": "rgba(255, 217, 61, 0.1)",
                                "borderRadius": AI_EFFECTS['border_radius_sm'],
                                "border": "1px solid rgba(255, 217, 61, 0.3)",
                                "color": AI_COLORS['dark']
                            })
                            for insight in ai_insights
                        ])
                    ], className="recommendations-container", style={
                        "maxHeight": "250px",
                        "overflowY": "auto"
                    })
                ])
                    ], className="card-body", style={
                        "padding": f"{AI_SPACING['xl']} {AI_SPACING['xl']}",
                        "background": "transparent"
                    })
                ], className="card h-100")
            ], style=get_card_style('secondary'))
        ], className="ai-recommendations-panel")

    except Exception as e:
        logger.error(f"Error creating AI recommendations panel: {str(e)}")
        return create_placeholder_card("🎯 AI Recommendations", f"Error: {str(e)}")


def create_ai_regime_context_panel(bundle_data: FinalAnalysisBundleV2_5, ai_settings: Dict[str, Any], regime: str) -> html.Div:
    """Create enhanced AI regime analysis panel with 4-quadrant layout similar to Unified AI Intelligence Hub."""
    try:
        # Generate enhanced regime analysis with EOTS metrics
        regime_analysis = analyze_enhanced_regime_with_ai(bundle_data, regime)

        # Create enhanced regime confidence visualization
        regime_confidence = calculate_enhanced_regime_confidence(bundle_data, regime)

        # Extract EOTS metrics for regime context using Pydantic models
        processed_data = bundle_data.processed_data_bundle
        metrics = processed_data.underlying_data_enriched.model_dump() if processed_data else {}

        # Calculate regime transition probability
        transition_prob = calculate_regime_transition_probability(bundle_data, regime)

        # Get regime characteristics
        regime_characteristics = get_regime_characteristics(regime, metrics)

        # Calculate metric confluence score
        confluence_score = calculate_metric_confluence_score(metrics)

        # Calculate signal strength for quadrant 2
        signal_strength = assess_signal_strength(metrics)

        # UNIFIED 4-QUADRANT LAYOUT STRUCTURE
        return html.Div([
            # Outer colored container
            html.Div([
                # Inner dark card container
                html.Div([
                    # Card Header with clickable title and info
                    html.Div([
                        create_clickable_title_with_info(
                            "🌊 AI Regime Analysis",
                            "regime_analysis",
                            AI_MODULE_INFO["regime_analysis"]
                        )
                    ], className="card-header", style={
                        "background": "transparent",
                        "borderBottom": f"2px solid {AI_COLORS['success']}",
                        "padding": f"{AI_SPACING['md']} {AI_SPACING['xl']}"
                    }),

                    # Card Body with 4-Quadrant Layout
                    html.Div([
                        # TOP ROW - Quadrants 1 & 2
                        html.Div([
                            # QUADRANT 1: Regime Confidence Barometer (Top Left)
                            html.Div([
                                create_regime_confidence_barometer(regime_confidence, regime, transition_prob)
                            ], className="col-md-6"),

                            # QUADRANT 2: Regime Characteristics Analysis (Top Right)
                            html.Div([
                                create_regime_characteristics_analysis(regime_characteristics, regime, signal_strength)
                            ], className="col-md-6")
                        ], className="row mb-4"),

                        # BOTTOM ROW - Quadrants 3 & 4
                        html.Div([
                            # QUADRANT 3: Enhanced AI Regime Analysis (Bottom Left)
                            html.Div([
                                create_enhanced_regime_analysis_quadrant(regime_analysis, regime, metrics)
                            ], className="col-md-6"),

                            # QUADRANT 4: Regime Transition Gauge (Bottom Right)
                            html.Div([
                                create_regime_transition_gauge_quadrant(regime_confidence, transition_prob, regime, confluence_score)
                            ], className="col-md-6")
                        ], className="row")
                    ], className="card-body", style={
                        "padding": f"{AI_SPACING['lg']} {AI_SPACING['xl']}",
                        "background": "transparent"
                    })
                ], className="card h-100")
            ], style=get_card_style('success'))
        ], className="ai-regime-analysis-panel")

    except Exception as e:
        logger.error(f"Error creating AI regime analysis panel: {str(e)}")
        return create_placeholder_card(f"🌊 Regime Analysis: {regime}", f"Error: {str(e)}")


# ===== AI REGIME ANALYSIS 4-QUADRANT FUNCTIONS =====

def create_regime_confidence_barometer(regime_confidence: float, regime: str, transition_prob: float) -> html.Div:
    """QUADRANT 1: Create Regime Confidence Barometer with detailed breakdown."""
    try:
        # Get tactical regime name and styling
        tactical_regime_name = get_tactical_regime_name(regime)

        # Determine confidence level and styling
        if regime_confidence >= 0.8:
            confidence_level = "Very High"
            color = AI_COLORS['success']
            icon = "🔥"
            bg_color = "rgba(107, 207, 127, 0.1)"
        elif regime_confidence >= 0.6:
            confidence_level = "High"
            color = AI_COLORS['primary']
            icon = "⚡"
            bg_color = "rgba(0, 212, 255, 0.1)"
        elif regime_confidence >= 0.4:
            confidence_level = "Moderate"
            color = AI_COLORS['warning']
            icon = "⚠️"
            bg_color = "rgba(255, 167, 38, 0.1)"
        else:
            confidence_level = "Low"
            color = AI_COLORS['danger']
            icon = "🚨"
            bg_color = "rgba(255, 71, 87, 0.1)"

        return html.Div([
            html.Div([
                html.H6(f"{icon} Regime Confidence", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Main confidence display
                html.Div([
                    html.Div([
                        html.Span(f"{regime_confidence:.0%}", id="regime-confidence-score", style={
                            "fontSize": "2.5rem",
                            "fontWeight": "bold",
                            "color": color
                        }),
                        html.Div(confidence_level, style={
                            "fontSize": AI_TYPOGRAPHY['body_size'],
                            "color": AI_COLORS['muted'],
                            "marginTop": "-5px"
                        })
                    ], className="text-center mb-3"),

                    # Enhanced Confidence bar
                    html.Div([
                        html.Div(style={
                            "width": f"{regime_confidence * 100}%",
                            "height": "18px",
                            "background": f"linear-gradient(90deg, {color}, {color}aa)",
                            "borderRadius": "9px",
                            "transition": AI_EFFECTS['transition']
                        })
                    ], style={
                        "width": "100%",
                        "height": "18px",
                        "background": "rgba(255, 255, 255, 0.1)",
                        "borderRadius": "9px",
                        "marginBottom": AI_SPACING['lg']
                    }),

                    # Regime details
                    html.Div([
                        html.Div([
                            html.Small("Current Regime: ", style={"color": AI_COLORS['muted']}),
                            html.Small(tactical_regime_name, style={"color": color, "fontWeight": "bold"})
                        ], className="mb-2"),
                        html.Div([
                            html.Small("Transition Risk: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{transition_prob:.0%}", id="regime-transition-prob", style={
                                "color": AI_COLORS['danger'] if transition_prob > 0.6 else AI_COLORS['warning'] if transition_prob > 0.3 else AI_COLORS['success'],
                                "fontWeight": "bold"
                            })
                        ])
                    ])
                ])
            ], id="regime-analysis-container", style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {color}",
                "height": "100%",
                "minHeight": "280px"  # Match regime characteristics height
            })
        ])

    except Exception as e:
        logger.error(f"Error creating regime confidence barometer: {str(e)}")
        return html.Div("Regime confidence unavailable")


def create_regime_characteristics_analysis(regime_characteristics: Dict[str, str], regime: str, signal_strength: str) -> html.Div:
    """QUADRANT 2: Create Regime Characteristics Analysis with 4-quadrant layout and dynamic colors."""
    try:
        # Determine styling based on regime
        regime_colors = {
            'BULLISH': AI_COLORS['success'],
            'BEARISH': AI_COLORS['danger'],
            'NEUTRAL': AI_COLORS['warning'],
            'VOLATILE': AI_COLORS['info'],
            'UNKNOWN': AI_COLORS['muted']
        }

        regime_icons = {
            'BULLISH': '🚀',
            'BEARISH': '🐻',
            'NEUTRAL': '⚖️',
            'VOLATILE': '🌪️',
            'UNKNOWN': '❓'
        }

        color = regime_colors.get(regime, AI_COLORS['muted'])
        icon = regime_icons.get(regime, '📊')
        bg_color = f"rgba({color[4:-1]}, 0.1)"

        # Function to get dynamic color for characteristic value
        def get_characteristic_color(char_value: str) -> str:
            """Get dynamic color based on characteristic value."""
            char_value_lower = char_value.lower()
            if any(word in char_value_lower for word in ['high', 'strong', 'positive', 'expanding', 'elevated']):
                return AI_COLORS['success']
            elif any(word in char_value_lower for word in ['low', 'weak', 'negative', 'contracting']):
                return AI_COLORS['danger']
            elif any(word in char_value_lower for word in ['moderate', 'medium', 'balanced', 'neutral']):
                return AI_COLORS['warning']
            elif any(word in char_value_lower for word in ['very high', 'extreme', 'massive']):
                return AI_COLORS['info']
            else:
                return AI_COLORS['secondary']

        # Function to get background color for characteristic container
        def get_characteristic_bg_color(char_value: str) -> str:
            """Get background color for characteristic container."""
            char_color = get_characteristic_color(char_value)
            # Extract RGB values and create transparent version
            if char_color.startswith('#'):
                # Convert hex to rgba
                hex_color = char_color.lstrip('#')
                rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                return f"rgba({rgb[0]}, {rgb[1]}, {rgb[2]}, 0.1)"
            else:
                return "rgba(255, 255, 255, 0.05)"

        return html.Div([
            html.Div([
                # Header with signal strength indicator
                html.Div([
                    html.H6(f"{icon} Regime Characteristics", className="mb-0", style={
                        "color": AI_COLORS['dark'],
                        "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                        "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                    }),
                    html.Div([
                        html.Small("Signal Strength: ", style={"color": AI_COLORS['muted']}),
                        html.Span(signal_strength, style={
                            "color": color,
                            "fontWeight": "bold",
                            "fontSize": AI_TYPOGRAPHY['body_size']
                        })
                    ])
                ], className="d-flex justify-content-between align-items-center mb-3"),

                # 4-QUADRANT CHARACTERISTICS LAYOUT
                html.Div([
                    # TOP ROW - First 2 characteristics
                    html.Div([
                        html.Div([
                            create_characteristic_quadrant(char_name, char_value, get_characteristic_color(char_value), get_characteristic_bg_color(char_value))
                            for char_name, char_value in list(regime_characteristics.items())[:2]
                        ], className="row mb-2"),

                        # BOTTOM ROW - Last 2 characteristics
                        html.Div([
                            html.Div([
                                create_characteristic_quadrant(char_name, char_value, get_characteristic_color(char_value), get_characteristic_bg_color(char_value))
                                for char_name, char_value in list(regime_characteristics.items())[2:4]
                            ], className="row")
                        ])
                    ])
                ], id="regime-characteristics")
            ], style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {color}",
                "height": "100%",
                "minHeight": "280px"  # Match regime confidence height
            })
        ])

    except Exception as e:
        logger.error(f"Error creating regime characteristics analysis: {str(e)}")
        return html.Div("Regime characteristics unavailable")


def create_characteristic_quadrant(char_name: str, char_value: str, char_color: str, bg_color: str) -> html.Div:
    """Create individual characteristic quadrant with dynamic styling."""
    return html.Div([
        html.Div([
            html.Small(char_name, className="d-block mb-1", style={
                "fontSize": AI_TYPOGRAPHY['tiny_size'],
                "color": AI_COLORS['muted'],
                "fontWeight": "500"
            }),
            html.Strong(char_value, style={
                "fontSize": AI_TYPOGRAPHY['small_size'],
                "color": char_color,
                "fontWeight": "bold"
            })
        ], className="text-center", style={
            "padding": f"{AI_SPACING['sm']} {AI_SPACING['xs']}",
            "background": bg_color,
            "borderRadius": AI_EFFECTS['border_radius_sm'],
            "border": f"1px solid {char_color}",
            "height": "60px",
            "display": "flex",
            "flexDirection": "column",
            "justifyContent": "center",
            "transition": AI_EFFECTS['transition'],
            "cursor": "default"
        })
    ], className="col-6 mb-1")


def create_enhanced_regime_analysis_quadrant(regime_analysis: List[str], regime: str, metrics: Dict[str, Any]) -> html.Div:
    """QUADRANT 3: Create Enhanced AI Regime Analysis with key insights."""
    try:
        # Get tactical regime name and styling
        tactical_regime_name = get_tactical_regime_name(regime)

        # Determine regime styling
        regime_colors = {
            'BULLISH': AI_COLORS['success'],
            'BEARISH': AI_COLORS['danger'],
            'NEUTRAL': AI_COLORS['warning'],
            'VOLATILE': AI_COLORS['info'],
            'UNKNOWN': AI_COLORS['muted']
        }

        color = regime_colors.get(regime, AI_COLORS['muted'])
        bg_color = f"rgba({color[4:-1]}, 0.1)"

        # Get key metrics for display
        vapi_fa = metrics.get('vapi_fa_z_score_und', 0.0)
        dwfd = metrics.get('dwfd_z_score_und', 0.0)
        tw_laf = metrics.get('tw_laf_z_score_und', 0.0)

        return html.Div([
            html.Div([
                html.H6(f"🧠 Enhanced AI Analysis", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Current regime display
                html.Div([
                    html.Div([
                        html.Span(f"🌊 {tactical_regime_name}", style={
                            "fontSize": AI_TYPOGRAPHY['title_size'],
                            "fontWeight": "bold",
                            "color": color
                        })
                    ], className="text-center mb-3"),

                    # Key metrics summary
                    html.Div([
                        html.Div([
                            html.Small("VAPI-FA: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{vapi_fa:.2f}σ", style={
                                "color": AI_COLORS['success'] if vapi_fa > 0 else AI_COLORS['danger'],
                                "fontWeight": "bold"
                            })
                        ], className="mb-1"),
                        html.Div([
                            html.Small("DWFD: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{dwfd:.2f}σ", style={
                                "color": AI_COLORS['success'] if dwfd > 0 else AI_COLORS['danger'],
                                "fontWeight": "bold"
                            })
                        ], className="mb-1"),
                        html.Div([
                            html.Small("TW-LAF: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{tw_laf:.2f}σ", style={
                                "color": AI_COLORS['success'] if tw_laf > 0 else AI_COLORS['danger'],
                                "fontWeight": "bold"
                            })
                        ], className="mb-3")
                    ]),

                    # AI Analysis insights
                    html.Div([
                        html.Div([
                            html.P(analysis, className="small mb-2", style={
                                "fontSize": AI_TYPOGRAPHY['small_size'],
                                "lineHeight": "1.4",
                                "color": AI_COLORS['dark'],
                                "padding": AI_SPACING['sm'],
                                "borderLeft": f"3px solid {color}",
                                "backgroundColor": "rgba(255, 255, 255, 0.05)",
                                "borderRadius": AI_EFFECTS['border_radius_sm'],
                                "margin": "0 0 8px 0"
                            })
                            for analysis in regime_analysis[:3]  # Limit to top 3 insights
                        ])
                    ])
                ])
            ], style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {color}",
                "height": "100%"
            })
        ])

    except Exception as e:
        logger.error(f"Error creating enhanced regime analysis quadrant: {str(e)}")
        return html.Div("Enhanced regime analysis unavailable")


def create_regime_transition_gauge_quadrant(regime_confidence: float, transition_prob: float, regime: str, confluence_score: float) -> html.Div:
    """QUADRANT 4: Create Regime Transition Gauge with comprehensive metrics."""
    try:
        # Determine gauge styling based on transition probability
        if transition_prob >= 0.7:
            gauge_color = AI_COLORS['danger']
            gauge_level = "High Risk"
            gauge_icon = "🚨"
            bg_color = "rgba(255, 71, 87, 0.1)"
        elif transition_prob >= 0.4:
            gauge_color = AI_COLORS['warning']
            gauge_level = "Moderate Risk"
            gauge_icon = "⚠️"
            bg_color = "rgba(255, 167, 38, 0.1)"
        else:
            gauge_color = AI_COLORS['success']
            gauge_level = "Low Risk"
            gauge_icon = "✅"
            bg_color = "rgba(107, 207, 127, 0.1)"

        return html.Div([
            html.Div([
                html.H6(f"{gauge_icon} Transition Gauge", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Main gauge visualization
                html.Div([
                    dcc.Graph(
                        figure=create_regime_transition_gauge(transition_prob, regime_confidence, confluence_score),
                        config={'displayModeBar': False},
                        style={"height": "180px", "marginBottom": "10px"}
                    )
                ]),

                # Gauge metrics summary
                html.Div([
                    html.Div([
                        html.Small("Transition Risk: ", style={"color": AI_COLORS['muted']}),
                        html.Small(f"{transition_prob:.0%} ({gauge_level})", style={
                            "color": gauge_color,
                            "fontWeight": "bold"
                        })
                    ], className="mb-2"),
                    html.Div([
                        html.Small("Regime Stability: ", style={"color": AI_COLORS['muted']}),
                        html.Small(f"{(1-transition_prob):.0%}", style={
                            "color": AI_COLORS['success'] if (1-transition_prob) > 0.6 else AI_COLORS['warning'],
                            "fontWeight": "bold"
                        })
                    ], className="mb-2"),
                    html.Div([
                        html.Small("Signal Confluence: ", style={"color": AI_COLORS['muted']}),
                        html.Small(f"{confluence_score:.0%}", style={
                            "color": AI_COLORS['success'] if confluence_score > 0.6 else AI_COLORS['warning'],
                            "fontWeight": "bold"
                        })
                    ])
                ])
            ], style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {gauge_color}",
                "height": "100%"
            })
        ])

    except Exception as e:
        logger.error(f"Error creating regime transition gauge quadrant: {str(e)}")
        return html.Div("Regime transition gauge unavailable")


# ===== UTILITY FUNCTIONS =====

def get_regime_characteristics(regime: str, metrics: Dict[str, Any]) -> Dict[str, str]:
    """Get regime characteristics based on current regime and metrics."""
    try:
        # Base characteristics for all regimes
        characteristics = {
            "Volatility": "Moderate",
            "Flow Direction": "Balanced",
            "Risk Level": "Medium",
            "Momentum": "Neutral"
        }

        # Regime-specific characteristics
        if 'BULLISH' in regime:
            characteristics.update({
                "Volatility": "Expanding",
                "Flow Direction": "Call Bias",
                "Risk Level": "Elevated",
                "Momentum": "Positive"
            })
        elif 'BEARISH' in regime:
            characteristics.update({
                "Volatility": "Contracting",
                "Flow Direction": "Put Bias",
                "Risk Level": "High",
                "Momentum": "Negative"
            })
        elif 'CONSOLIDATION' in regime:
            characteristics.update({
                "Volatility": "Low",
                "Flow Direction": "Balanced",
                "Risk Level": "Low",
                "Momentum": "Range-bound"
            })
        elif 'VOLATILE' in regime or 'VOL_EXPANSION' in regime:
            characteristics.update({
                "Volatility": "High",
                "Flow Direction": "Mixed",
                "Risk Level": "Very High",
                "Momentum": "Unstable"
            })

        return characteristics

    except Exception as e:
        logger.error(f"Error getting regime characteristics: {str(e)}")
        return {"Status": "Unknown", "Risk": "Unknown", "Flow": "Unknown", "Trend": "Unknown"}


def get_color_for_value(value: float) -> str:
    """Get color based on value (positive/negative)."""
    if value > 0.1:
        return AI_COLORS['success']
    elif value < -0.1:
        return AI_COLORS['danger']
    else:
        return AI_COLORS['warning']


def get_confluence_color(confluence_score: float) -> str:
    """Get color based on confluence score."""
    if confluence_score >= 0.8:
        return AI_COLORS['success']
    elif confluence_score >= 0.6:
        return AI_COLORS['primary']
    elif confluence_score >= 0.4:
        return AI_COLORS['warning']
    else:
        return AI_COLORS['danger']


def calculate_data_quality_score(bundle_data: FinalAnalysisBundleV2_5) -> float:
    """Calculate data quality score for confidence barometer."""
    try:
        # Check if we have processed data
        if not bundle_data.processed_data_bundle:
            return 0.3

        # Check if we have underlying data
        if not bundle_data.processed_data_bundle.underlying_data_enriched:
            return 0.5

        # Check if we have strike data
        if not bundle_data.processed_data_bundle.strike_level_data_with_metrics:
            return 0.7

        # All data available
        return 0.9

    except Exception as e:
        logger.error(f"Error calculating data quality score: {str(e)}")
        return 0.5


def count_bullish_signals(metrics: Dict[str, Any]) -> int:
    """Count bullish signals from metrics."""
    try:
        count = 0

        # VAPI-FA bullish
        if metrics.get('vapi_fa_z_score_und', 0) > 1.0:
            count += 1

        # DWFD bullish
        if metrics.get('dwfd_z_score_und', 0) > 0.5:
            count += 1

        # TW-LAF bullish
        if metrics.get('tw_laf_z_score_und', 0) > 1.0:
            count += 1

        # GIB bullish (positive call imbalance)
        if metrics.get('gib_oi_based_und', 0) > 50000:
            count += 1

        return count

    except Exception as e:
        logger.error(f"Error counting bullish signals: {str(e)}")
        return 0


def count_bearish_signals(metrics: Dict[str, Any]) -> int:
    """Count bearish signals from metrics."""
    try:
        count = 0

        # VAPI-FA bearish
        if metrics.get('vapi_fa_z_score_und', 0) < -1.0:
            count += 1

        # DWFD bearish
        if metrics.get('dwfd_z_score_und', 0) < -0.5:
            count += 1

        # TW-LAF bearish
        if metrics.get('tw_laf_z_score_und', 0) < -1.0:
            count += 1

        # GIB bearish (negative put imbalance)
        if metrics.get('gib_oi_based_und', 0) < -50000:
            count += 1

        return count

    except Exception as e:
        logger.error(f"Error counting bearish signals: {str(e)}")
        return 0


def count_neutral_signals(metrics: Dict[str, Any]) -> int:
    """Count neutral signals from metrics."""
    try:
        count = 0

        # VAPI-FA neutral
        vapi_fa = abs(metrics.get('vapi_fa_z_score_und', 0))
        if vapi_fa <= 1.0:
            count += 1

        # DWFD neutral
        dwfd = abs(metrics.get('dwfd_z_score_und', 0))
        if dwfd <= 0.5:
            count += 1

        # TW-LAF neutral
        tw_laf = abs(metrics.get('tw_laf_z_score_und', 0))
        if tw_laf <= 1.0:
            count += 1

        # GIB neutral
        gib = abs(metrics.get('gib_oi_based_und', 0))
        if gib <= 50000:
            count += 1

        return count

    except Exception as e:
        logger.error(f"Error counting neutral signals: {str(e)}")
        return 0


def create_atif_recommendation_items(atif_recs: List[Any]) -> html.Div:
    """Create ATIF recommendation items display."""
    try:
        if not atif_recs:
            return html.P("No ATIF recommendations available", style=get_unified_text_style('muted'))

        items = []
        for i, rec in enumerate(atif_recs):
            conviction = rec.final_conviction_score_from_atif
            strategy = rec.selected_strategy_type
            rationale = str(rec.supportive_rationale_components.get('primary_rationale', 'No rationale provided'))

            items.append(
                html.Div([
                    html.H6(f"🎯 #{i+1}: {strategy}", className="mb-2", style={
                        "fontSize": AI_TYPOGRAPHY['body_size'],
                        "fontWeight": AI_TYPOGRAPHY['subtitle_weight'],
                        "color": AI_COLORS['dark']
                    }),
                    html.Div([
                        html.Span("Conviction: ", className="fw-bold", style={
                            "fontSize": AI_TYPOGRAPHY['small_size'],
                            "color": AI_COLORS['dark']
                        }),
                        html.Span(f"{conviction:.1%}",
                                className=f"badge bg-{'success' if conviction > 0.7 else 'warning' if conviction > 0.5 else 'secondary'}",
                                style={"fontSize": AI_TYPOGRAPHY['small_size']})
                    ], className="mb-2"),
                    html.P(rationale[:120] + "..." if len(rationale) > 120 else rationale,
                           className="small text-muted", style={
                               "fontSize": AI_TYPOGRAPHY['small_size'],
                               "color": AI_COLORS['muted'],
                               "lineHeight": "1.3",
                               "marginBottom": "0"
                           })
                ], className="recommendation-item p-2 mb-2", style={
                    "background": "rgba(255, 217, 61, 0.1)",
                    "borderRadius": AI_EFFECTS['border_radius_sm'],
                    "border": "1px solid rgba(255, 217, 61, 0.3)",
                    "transition": AI_EFFECTS['transition'],
                    "cursor": "pointer"
                })
            )

        return html.Div(items)

    except Exception as e:
        logger.error(f"Error creating ATIF recommendation items: {str(e)}")
        return html.P("Error displaying ATIF recommendations", style=get_unified_text_style('muted'))


def calculate_metric_confluence_score(metrics: Dict[str, Any]) -> float:
    """Calculate metric confluence score for unified intelligence."""
    try:
        # Extract key flow metrics
        vapi_fa = abs(metrics.get('vapi_fa_z_score_und', 0))
        dwfd = abs(metrics.get('dwfd_z_score_und', 0))
        tw_laf = abs(metrics.get('tw_laf_z_score_und', 0))

        # Calculate confluence based on signal alignment
        strong_signals = sum([vapi_fa > 1.5, dwfd > 1.5, tw_laf > 1.5])
        signal_strength = (vapi_fa + dwfd + tw_laf) / 3.0

        # Confluence score combines signal count and strength
        confluence = (strong_signals / 3.0) * 0.6 + min(signal_strength / 3.0, 1.0) * 0.4
        return min(confluence, 1.0)

    except Exception as e:
        logger.error(f"Error calculating confluence score: {str(e)}")
        return 0.5


def assess_signal_strength(metrics: Dict[str, Any]) -> str:
    """Assess overall signal strength from metrics."""
    try:
        # Calculate total signal strength
        vapi_fa = abs(metrics.get('vapi_fa_z_score_und', 0))
        dwfd = abs(metrics.get('dwfd_z_score_und', 0))
        tw_laf = abs(metrics.get('tw_laf_z_score_und', 0))

        total_strength = vapi_fa + dwfd + tw_laf

        if total_strength > 6.0:
            return "Extreme"
        elif total_strength > 4.0:
            return "Strong"
        elif total_strength > 2.0:
            return "Moderate"
        else:
            return "Weak"

    except Exception as e:
        logger.error(f"Error assessing signal strength: {str(e)}")
        return "Unknown"


# Import the badge style function from components
from .components import get_unified_badge_style

# ===== 4-QUADRANT FUNCTIONS =====

def create_ai_confidence_barometer(confidence_score: float, bundle_data: FinalAnalysisBundleV2_5, db_manager=None) -> html.Div:
    """QUADRANT 1: Create AI Confidence Barometer with detailed breakdown."""
    try:
        # Determine confidence level and styling
        if confidence_score >= 0.8:
            confidence_level = "Exceptional"
            color = AI_COLORS['success']
            icon = "🔥"
            bg_color = "rgba(107, 207, 127, 0.1)"
        elif confidence_score >= 0.6:
            confidence_level = "High"
            color = AI_COLORS['primary']
            icon = "⚡"
            bg_color = "rgba(0, 212, 255, 0.1)"
        elif confidence_score >= 0.4:
            confidence_level = "Moderate"
            color = AI_COLORS['warning']
            icon = "⚠️"
            bg_color = "rgba(255, 167, 38, 0.1)"
        else:
            confidence_level = "Low"
            color = AI_COLORS['danger']
            icon = "🚨"
            bg_color = "rgba(255, 71, 87, 0.1)"

        # Calculate confidence factors breakdown
        system_health = calculate_system_health_score(bundle_data, db_manager)
        data_quality = calculate_data_quality_score(bundle_data)

        return html.Div([
            html.Div([
                html.H6(f"{icon} AI Confidence Barometer", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Main confidence display
                html.Div([
                    html.Div([
                        html.Span(f"{confidence_score:.0%}", id="ai-confidence-score", style={
                            "fontSize": "2.5rem",
                            "fontWeight": "bold",
                            "color": color
                        }),
                        html.Div(confidence_level, id="ai-confidence-level", style={
                            "fontSize": AI_TYPOGRAPHY['body_size'],
                            "color": AI_COLORS['muted'],
                            "marginTop": "-5px"
                        })
                    ], className="text-center mb-3"),

                    # Enhanced Confidence bar - Bigger to fill timestamp space
                    html.Div([
                        html.Div(style={
                            "width": f"{confidence_score * 100}%",
                            "height": "18px",  # Increased from 12px to 18px
                            "background": f"linear-gradient(90deg, {color}, {color}aa)",
                            "borderRadius": "9px",  # Adjusted border radius proportionally
                            "transition": AI_EFFECTS['transition']
                        })
                    ], style={
                        "width": "100%",
                        "height": "18px",  # Match inner height
                        "background": "rgba(255, 255, 255, 0.1)",
                        "borderRadius": "9px",
                        "marginBottom": AI_SPACING['lg']  # Increased margin to fill space
                    }),

                    # Enhanced Confidence factors - Better spacing
                    html.Div([
                        html.Div([
                            html.Small("System Health: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{system_health:.0%}", style={"color": color, "fontWeight": "bold"})
                        ], className="mb-2"),
                        html.Div([
                            html.Small("Data Quality: ", style={"color": AI_COLORS['muted']}),
                            html.Small(f"{data_quality:.0%}", style={"color": color, "fontWeight": "bold"})
                        ])
                    ])
                ])
            ], id="ai-confidence-container", style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {color}",
                "height": "100%"
            })
        ])

    except Exception as e:
        logger.error(f"Error creating AI confidence barometer: {str(e)}")
        return html.Div("Confidence barometer unavailable")


def create_signal_confluence_barometer(confluence_score: float, metrics: Dict[str, Any], signal_strength: str) -> html.Div:
    """QUADRANT 2: Create Signal Confluence Barometer with signal breakdown."""
    try:
        # Determine confluence level and styling
        if confluence_score >= 0.8:
            confluence_level = "Strong Alignment"
            color = AI_COLORS['success']
            icon = "🎯"
            bg_color = "rgba(107, 207, 127, 0.1)"
        elif confluence_score >= 0.6:
            confluence_level = "Good Alignment"
            color = AI_COLORS['primary']
            icon = "📊"
            bg_color = "rgba(0, 212, 255, 0.1)"
        elif confluence_score >= 0.4:
            confluence_level = "Mixed Signals"
            color = AI_COLORS['warning']
            icon = "⚖️"
            bg_color = "rgba(255, 167, 38, 0.1)"
        else:
            confluence_level = "Divergent"
            color = AI_COLORS['danger']
            icon = "🌪️"
            bg_color = "rgba(255, 71, 87, 0.1)"

        # Calculate signal components
        bullish_signals = count_bullish_signals(metrics)
        bearish_signals = count_bearish_signals(metrics)
        neutral_signals = count_neutral_signals(metrics)
        total_signals = bullish_signals + bearish_signals + neutral_signals

        return html.Div([
            html.Div([
                # Header row with title and signal strength
                html.Div([
                    html.H6(f"{icon} Signal Confluence", className="mb-0", style={
                        "color": AI_COLORS['dark'],
                        "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                        "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                    }),
                    html.Div([
                        html.Small("Signal Strength: ", style={"color": AI_COLORS['muted']}),
                        html.Span(signal_strength, style={
                            "color": color,
                            "fontWeight": "bold",
                            "fontSize": AI_TYPOGRAPHY['body_size']
                        })
                    ])
                ], className="d-flex justify-content-between align-items-center mb-3"),

                # Main confluence display
                html.Div([
                    html.Div([
                        html.Span(f"{confluence_score:.0%}", id="signal-confluence-score", style={
                            "fontSize": "2.5rem",
                            "fontWeight": "bold",
                            "color": color
                        }),
                        html.Div(confluence_level, id="signal-confluence-level", style={
                            "fontSize": AI_TYPOGRAPHY['body_size'],
                            "color": AI_COLORS['muted'],
                            "marginTop": "-5px"
                        })
                    ], className="text-center mb-3"),

                    # Signal breakdown - Balanced spacing
                    html.Div([
                        html.Div([
                            html.Div([
                                html.Span("🟢", style={"marginRight": "5px"}),
                                html.Small(f"Bullish: {bullish_signals}", style={"color": AI_COLORS['success']})
                            ], className="d-flex justify-content-between mb-1"),
                            html.Div([
                                html.Span("🔴", style={"marginRight": "5px"}),
                                html.Small(f"Bearish: {bearish_signals}", style={"color": AI_COLORS['danger']})
                            ], className="d-flex justify-content-between mb-1"),
                            html.Div([
                                html.Span("⚪", style={"marginRight": "5px"}),
                                html.Small(f"Neutral: {neutral_signals}", style={"color": AI_COLORS['muted']})
                            ], className="d-flex justify-content-between")
                        ])
                    ])
                ])
            ], id="signal-confluence-container", style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": bg_color,
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {color}",
                "height": "100%"
            })
        ])

    except Exception as e:
        logger.error(f"Error creating signal confluence barometer: {str(e)}")
        return html.Div("Signal confluence unavailable")





def create_unified_intelligence_analysis(unified_insights: List[str], regime: str, bundle_data: FinalAnalysisBundleV2_5) -> html.Div:
    """QUADRANT 3: Create Unified Intelligence Analysis with key insights."""
    try:
        # Get tactical regime name
        tactical_regime_name = get_tactical_regime_name(regime)

        # Determine regime styling
        regime_colors = {
            'BULLISH': AI_COLORS['success'],
            'BEARISH': AI_COLORS['danger'],
            'NEUTRAL': AI_COLORS['warning'],
            'VOLATILE': AI_COLORS['info'],
            'UNKNOWN': AI_COLORS['muted']
        }

        regime_icons = {
            'BULLISH': '🚀',
            'BEARISH': '🐻',
            'NEUTRAL': '⚖️',
            'VOLATILE': '🌪️',
            'UNKNOWN': '❓'
        }

        regime_color = regime_colors.get(regime, AI_COLORS['muted'])
        regime_icon = regime_icons.get(regime, '❓')

        return html.Div([
            html.Div([
                html.H6(f"🧠 Unified Intelligence Analysis", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Market regime indicator
                html.Div([
                    html.Div([
                        html.Span(f"{regime_icon} {tactical_regime_name}", style={
                            "fontSize": AI_TYPOGRAPHY['title_size'],
                            "fontWeight": "bold",
                            "color": regime_color
                        }),
                        html.Div("Market Regime", style={
                            "fontSize": AI_TYPOGRAPHY['small_size'],
                            "color": AI_COLORS['muted'],
                            "marginTop": "-5px"
                        })
                    ], className="text-center mb-3")
                ]),

                # Key insights scrollable container
                html.Div([
                    html.Div([
                        html.Div([
                            html.Span("💡", style={"marginRight": "8px", "fontSize": "14px"}),
                            html.Span(insight, style={
                                "fontSize": AI_TYPOGRAPHY['small_size'],
                                "color": AI_COLORS['dark'],
                                "lineHeight": "1.4"
                            })
                        ], style={
                            "padding": f"{AI_SPACING['sm']} {AI_SPACING['md']}",
                            "background": "rgba(255, 255, 255, 0.05)",
                            "borderRadius": AI_EFFECTS['border_radius_sm'],
                            "marginBottom": AI_SPACING['sm'],
                            "border": "1px solid rgba(255, 255, 255, 0.1)"
                        })
                        for insight in unified_insights[:5]  # Show top 5 insights
                    ])
                ], style={
                    "maxHeight": "180px",
                    "overflowY": "auto",
                    "paddingRight": "5px"
                }),

                # Analysis timestamp
                html.Div([
                    html.Small(f"Analysis: {bundle_data.bundle_timestamp.strftime('%H:%M:%S')}",
                             style={"color": AI_COLORS['muted'], "fontSize": AI_TYPOGRAPHY['tiny_size']})
                ], className="text-center mt-2")
            ], style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": "rgba(0, 212, 255, 0.05)",
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"1px solid {AI_COLORS['primary']}",
                "height": "100%"
            })
        ])

    except Exception as e:
        logger.error(f"Error creating unified intelligence analysis: {str(e)}")
        return html.Div("Intelligence analysis unavailable")


def create_market_dynamics_radar_quadrant(enhanced_radar_fig, metrics: Dict[str, Any], symbol: str) -> html.Div:
    """QUADRANT 4: Enhanced Market Dynamics Radar - THE STAR OF THE SHOW."""
    try:
        # Calculate dynamic market forces
        momentum_force = calculate_momentum_force(metrics)
        volatility_force = calculate_volatility_force(metrics)
        volume_force = calculate_volume_force(metrics)
        sentiment_force = calculate_sentiment_force(metrics)

        # Determine dominant force
        forces = {
            'Momentum': momentum_force,
            'Volatility': volatility_force,
            'Volume': volume_force,
            'Sentiment': sentiment_force
        }
        dominant_force = max(forces, key=forces.get)
        dominant_value = forces[dominant_force]

        # Force icons and colors
        force_icons = {
            'Momentum': '🚀',
            'Volatility': '⚡',
            'Volume': '📊',
            'Sentiment': '💭'
        }

        return html.Div([
            html.Div([
                html.H6(f"🎯 Market Dynamics Radar", className="mb-3", style={
                    "color": AI_COLORS['dark'],
                    "fontSize": AI_TYPOGRAPHY['subtitle_size'],
                    "fontWeight": AI_TYPOGRAPHY['subtitle_weight']
                }),

                # Enhanced radar chart
                html.Div([
                    dcc.Graph(
                        id="market-dynamics-radar",
                        figure=enhanced_radar_fig,
                        config={'displayModeBar': False},
                        style={"height": "200px"}
                    )
                ], className="mb-3"),

                # Dominant force indicator
                html.Div([
                    html.Div([
                        html.Span(f"{force_icons[dominant_force]}", style={
                            "fontSize": "1.5rem",
                            "marginRight": "8px"
                        }),
                        html.Span(f"{dominant_force}", style={
                            "fontSize": AI_TYPOGRAPHY['body_size'],
                            "fontWeight": "bold",
                            "color": AI_COLORS['primary']
                        }),
                        html.Div(f"Dominant Force ({dominant_value:.1f})", style={
                            "fontSize": AI_TYPOGRAPHY['small_size'],
                            "color": AI_COLORS['muted']
                        })
                    ], className="text-center mb-2"),

                    # Force breakdown mini indicators
                    html.Div([
                        html.Div([
                            html.Span(f"{force_icons[force]}", style={"fontSize": "12px", "marginRight": "4px"}),
                            html.Span(f"{value:.1f}", style={
                                "fontSize": AI_TYPOGRAPHY['tiny_size'],
                                "color": AI_COLORS['primary'] if force == dominant_force else AI_COLORS['muted']
                            })
                        ], className="d-inline-block me-2")
                        for force, value in forces.items()
                    ], className="text-center")
                ])
            ], style={
                "padding": f"{AI_SPACING['lg']} {AI_SPACING['md']}",
                "background": "linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(107, 207, 127, 0.05))",
                "borderRadius": AI_EFFECTS['border_radius'],
                "border": f"2px solid {AI_COLORS['primary']}",
                "height": "100%",
                "boxShadow": AI_EFFECTS['box_shadow']
            })
        ])

    except Exception as e:
        logger.error(f"Error creating market dynamics radar quadrant: {str(e)}")
        return html.Div("Market dynamics radar unavailable")


def create_enhanced_market_dynamics_radar(bundle_data: FinalAnalysisBundleV2_5, symbol: str):
    """Create enhanced market dynamics radar with comprehensive market forces."""
    try:
        processed_data = bundle_data.processed_data_bundle
        metrics = processed_data.underlying_data_enriched.model_dump() if processed_data else {}

        # Calculate comprehensive market forces (0-10 scale)
        forces = {
            'Momentum': calculate_momentum_force(metrics),
            'Volatility': calculate_volatility_force(metrics),
            'Volume': calculate_volume_force(metrics),
            'Sentiment': calculate_sentiment_force(metrics),
            'Trend': calculate_trend_force(metrics),
            'Support/Resistance': calculate_sr_force(metrics),
            'Options Flow': calculate_options_flow_force(metrics),
            'Market Breadth': calculate_market_breadth_force(metrics)
        }

        # Create enhanced radar chart
        fig = go.Figure()

        # Add market forces trace
        fig.add_trace(go.Scatterpolar(
            r=list(forces.values()),
            theta=list(forces.keys()),
            fill='toself',
            fillcolor='rgba(0, 212, 255, 0.3)',
            line=dict(color=AI_COLORS['primary'], width=3),
            marker=dict(size=8, color=AI_COLORS['primary']),
            name='Market Forces',
            hovertemplate='<b>%{theta}</b><br>Force: %{r:.1f}/10<extra></extra>'
        ))

        # Add reference circle at 5.0 (neutral)
        fig.add_trace(go.Scatterpolar(
            r=[5] * len(forces),
            theta=list(forces.keys()),
            mode='lines',
            line=dict(color='rgba(255, 255, 255, 0.3)', width=1, dash='dash'),
            showlegend=False,
            hoverinfo='skip'
        ))

        # Enhanced layout
        fig.update_layout(
            polar=dict(
                bgcolor='rgba(0, 0, 0, 0.1)',
                radialaxis=dict(
                    visible=True,
                    range=[0, 10],
                    tickfont=dict(size=10, color=AI_COLORS['muted']),
                    gridcolor='rgba(255, 255, 255, 0.2)',
                    linecolor='rgba(255, 255, 255, 0.3)'
                ),
                angularaxis=dict(
                    tickfont=dict(size=11, color=AI_COLORS['dark']),
                    linecolor='rgba(255, 255, 255, 0.3)',
                    gridcolor='rgba(255, 255, 255, 0.2)'
                )
            ),
            showlegend=False,
            margin=dict(l=20, r=20, t=20, b=20),
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color=AI_COLORS['dark'])
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating enhanced market dynamics radar: {str(e)}")
        return go.Figure()


# ===== MARKET FORCE CALCULATION FUNCTIONS =====

def calculate_momentum_force(metrics: Dict[str, Any]) -> float:
    """Calculate momentum force (0-10 scale)."""
    try:
        # Extract momentum indicators
        rsi = metrics.get('rsi_14', 50)
        macd_signal = metrics.get('macd_signal', 0)
        price_change = metrics.get('price_change_percent', 0)

        # Normalize to 0-10 scale
        rsi_score = abs(rsi - 50) / 5  # 0-10 based on deviation from neutral
        macd_score = min(abs(macd_signal) * 100, 10)  # Scale MACD signal
        price_score = min(abs(price_change) * 2, 10)  # Scale price change

        return min((rsi_score + macd_score + price_score) / 3, 10)
    except:
        return 5.0

def calculate_volatility_force(metrics: Dict[str, Any]) -> float:
    """Calculate volatility force (0-10 scale)."""
    try:
        # Extract volatility indicators
        atr = metrics.get('atr_14', 0)
        bb_width = metrics.get('bollinger_width', 0)
        volume_volatility = metrics.get('volume_volatility', 0)

        # Normalize to 0-10 scale
        atr_score = min(atr * 100, 10)
        bb_score = min(bb_width * 50, 10)
        vol_score = min(volume_volatility * 10, 10)

        return min((atr_score + bb_score + vol_score) / 3, 10)
    except:
        return 5.0

def calculate_volume_force(metrics: Dict[str, Any]) -> float:
    """Calculate volume force (0-10 scale)."""
    try:
        # Extract volume indicators
        volume_ratio = metrics.get('volume_ratio', 1.0)
        volume_trend = metrics.get('volume_trend', 0)

        # Normalize to 0-10 scale
        ratio_score = min(volume_ratio * 5, 10)
        trend_score = min(abs(volume_trend) * 10, 10)

        return min((ratio_score + trend_score) / 2, 10)
    except:
        return 5.0

def calculate_sentiment_force(metrics: Dict[str, Any]) -> float:
    """Calculate sentiment force (0-10 scale)."""
    try:
        # Extract sentiment indicators
        put_call_ratio = metrics.get('put_call_ratio', 1.0)
        vix_level = metrics.get('vix', 20)

        # Normalize to 0-10 scale
        pc_score = min(abs(put_call_ratio - 1.0) * 20, 10)
        vix_score = min(vix_level / 3, 10)

        return min((pc_score + vix_score) / 2, 10)
    except:
        return 5.0

def calculate_trend_force(metrics: Dict[str, Any]) -> float:
    """Calculate trend force (0-10 scale)."""
    try:
        # Extract trend indicators
        ema_slope = metrics.get('ema_slope', 0)
        trend_strength = metrics.get('trend_strength', 0)

        # Normalize to 0-10 scale
        slope_score = min(abs(ema_slope) * 100, 10)
        strength_score = min(trend_strength * 10, 10)

        return min((slope_score + strength_score) / 2, 10)
    except:
        return 5.0

def calculate_sr_force(metrics: Dict[str, Any]) -> float:
    """Calculate support/resistance force (0-10 scale)."""
    try:
        # Extract S/R indicators
        distance_to_support = metrics.get('distance_to_support', 0.05)
        distance_to_resistance = metrics.get('distance_to_resistance', 0.05)

        # Normalize to 0-10 scale (closer = higher force)
        support_score = max(0, 10 - (distance_to_support * 200))
        resistance_score = max(0, 10 - (distance_to_resistance * 200))

        return min((support_score + resistance_score) / 2, 10)
    except:
        return 5.0

def calculate_options_flow_force(metrics: Dict[str, Any]) -> float:
    """Calculate options flow force (0-10 scale)."""
    try:
        # Extract options flow indicators
        call_volume = metrics.get('call_volume', 0)
        put_volume = metrics.get('put_volume', 0)
        total_volume = call_volume + put_volume

        if total_volume == 0:
            return 5.0

        # Normalize to 0-10 scale
        flow_imbalance = abs(call_volume - put_volume) / total_volume
        flow_score = min(flow_imbalance * 20, 10)

        return flow_score
    except:
        return 5.0

def calculate_market_breadth_force(metrics: Dict[str, Any]) -> float:
    """Calculate market breadth force (0-10 scale)."""
    try:
        # Extract breadth indicators
        advance_decline = metrics.get('advance_decline_ratio', 1.0)
        new_highs_lows = metrics.get('new_highs_lows_ratio', 1.0)

        # Normalize to 0-10 scale
        ad_score = min(abs(advance_decline - 1.0) * 20, 10)
        hl_score = min(abs(new_highs_lows - 1.0) * 20, 10)

        return min((ad_score + hl_score) / 2, 10)
    except:
        return 5.0


def get_confluence_color(confluence_score: float) -> str:
    """Get color for confluence score."""
    if confluence_score > 0.8:
        return AI_COLORS['primary']  # Bright blue for high confluence
    elif confluence_score > 0.6:
        return AI_COLORS['success']  # Green for good confluence
    elif confluence_score > 0.4:
        return AI_COLORS['warning']  # Yellow for moderate confluence
    else:
        return AI_COLORS['danger']  # Red for low confluence


def create_confluence_gauge(confluence_score: float):
    """Create a small confluence gauge visualization."""

    fig = go.Figure(go.Indicator(
        mode = "gauge+number",
        value = confluence_score * 100,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Confluence"},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': get_confluence_color(confluence_score)},
            'steps': [
                {'range': [0, 40], 'color': "rgba(255, 107, 107, 0.2)"},
                {'range': [40, 60], 'color': "rgba(255, 193, 7, 0.2)"},
                {'range': [60, 80], 'color': "rgba(107, 207, 127, 0.2)"},
                {'range': [80, 100], 'color': "rgba(0, 123, 255, 0.2)"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))

    fig.update_layout(
        height=120,
        margin=dict(l=20, r=20, t=30, b=20),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        font=dict(color=AI_COLORS['dark'], size=10)
    )

    return fig


# ===== SIGNAL ANALYSIS FUNCTIONS =====

def count_bullish_signals(metrics: Dict[str, Any]) -> int:
    """Count bullish signals from metrics."""
    try:
        bullish_count = 0

        # RSI conditions
        rsi = metrics.get('rsi_14', 50)
        if 30 <= rsi <= 70:  # Not oversold/overbought
            bullish_count += 1

        # MACD conditions
        macd = metrics.get('macd', 0)
        macd_signal = metrics.get('macd_signal', 0)
        if macd > macd_signal:
            bullish_count += 1

        # Price above moving averages
        price = metrics.get('current_price', 0)
        ema_20 = metrics.get('ema_20', 0)
        if price > ema_20:
            bullish_count += 1

        # Volume confirmation
        volume_ratio = metrics.get('volume_ratio', 1.0)
        if volume_ratio > 1.2:
            bullish_count += 1

        return bullish_count
    except:
        return 0

def count_bearish_signals(metrics: Dict[str, Any]) -> int:
    """Count bearish signals from metrics."""
    try:
        bearish_count = 0

        # RSI conditions
        rsi = metrics.get('rsi_14', 50)
        if rsi > 70:  # Overbought
            bearish_count += 1

        # MACD conditions
        macd = metrics.get('macd', 0)
        macd_signal = metrics.get('macd_signal', 0)
        if macd < macd_signal:
            bearish_count += 1

        # Price below moving averages
        price = metrics.get('current_price', 0)
        ema_20 = metrics.get('ema_20', 0)
        if price < ema_20:
            bearish_count += 1

        # High put/call ratio
        put_call_ratio = metrics.get('put_call_ratio', 1.0)
        if put_call_ratio > 1.2:
            bearish_count += 1

        return bearish_count
    except:
        return 0

def count_neutral_signals(metrics: Dict[str, Any]) -> int:
    """Count neutral signals from metrics."""
    try:
        neutral_count = 0

        # RSI in neutral zone
        rsi = metrics.get('rsi_14', 50)
        if 45 <= rsi <= 55:
            neutral_count += 1

        # Low volatility
        atr = metrics.get('atr_14', 0)
        if atr < 0.02:  # Low ATR indicates low volatility
            neutral_count += 1

        # Balanced volume
        volume_ratio = metrics.get('volume_ratio', 1.0)
        if 0.8 <= volume_ratio <= 1.2:
            neutral_count += 1

        return neutral_count
    except:
        return 0


# ===== SYSTEM HEALTH FUNCTIONS =====

def calculate_system_health_score(bundle_data: FinalAnalysisBundleV2_5, db_manager=None) -> float:
    """Calculate overall system health score."""
    try:
        health_factors = []

        # Data freshness
        time_diff = (datetime.now() - bundle_data.bundle_timestamp).total_seconds()
        freshness_score = max(0, 1 - (time_diff / 300))  # 5 minute decay
        health_factors.append(freshness_score)

        # Data completeness
        processed_data = bundle_data.processed_data_bundle
        if processed_data and processed_data.underlying_data_enriched:
            completeness_score = 0.9  # High if data exists
        else:
            completeness_score = 0.3
        health_factors.append(completeness_score)

        # Database connectivity (if available)
        if db_manager:
            try:
                # Simple connectivity test
                db_score = 1.0
            except:
                db_score = 0.5
        else:
            db_score = 0.8  # Assume good if no db_manager
        health_factors.append(db_score)

        return sum(health_factors) / len(health_factors)
    except:
        return 0.5

def calculate_data_quality_score(bundle_data: FinalAnalysisBundleV2_5) -> float:
    """Calculate data quality score."""
    try:
        quality_factors = []

        processed_data = bundle_data.processed_data_bundle
        if not processed_data:
            return 0.3

        # Check for required fields
        enriched_data = processed_data.underlying_data_enriched
        if enriched_data:
            # Check if key metrics exist
            metrics = enriched_data.model_dump()
            required_fields = ['current_price', 'volume', 'rsi_14', 'macd']

            field_score = sum(1 for field in required_fields if metrics.get(field) is not None) / len(required_fields)
            quality_factors.append(field_score)

            # Check for reasonable values
            price = metrics.get('current_price', 0)
            volume = metrics.get('volume', 0)

            if price > 0 and volume > 0:
                value_score = 1.0
            else:
                value_score = 0.5
            quality_factors.append(value_score)
        else:
            quality_factors.append(0.3)

        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.3
    except:
        return 0.3
